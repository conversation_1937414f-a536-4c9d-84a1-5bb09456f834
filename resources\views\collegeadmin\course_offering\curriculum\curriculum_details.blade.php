<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('title', 'CLASSMOSS - Curriculum Details for ' . $program->program_code . ' (' . $curriculumYear . ')')

@section('main-content')
<section class="content-header">
    <h1>
        Curriculum Details for {{ $program->program_code }} ({{ $curriculumYear }})
        <small>{{ $program->program_name }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{ url('/collegeadmin/dashboard') }}"><i class="fa fa-dashboard"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.course_offering_curriculum.index') }}">Course Offering Curriculum</a></li>
        <li><a href="{{ route('collegeadmin.course_offering_curriculum.program', $program->program_code) }}">{{ $program->program_code }}</a></li>
        <li class="active">{{ $curriculumYear }}</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Curriculum Subjects</h3>
                </div>
                <div class="box-body">
                    @if(Session::has('success'))
                        <div class="alert alert-success">
                            {{ Session::get('success') }}
                        </div>
                    @endif

                    @if(Session::has('error'))
                        <div class="alert alert-danger">
                            {{ Session::get('error') }}
                        </div>
                    @endif

                    @if(count($subjects) > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Course Code</th>
                                        <th>Course Name</th>
                                        <th>Lec</th>
                                        <th>Lab</th>
                                        <th>Units</th>
                                        <th>Level</th>
                                        <th>Period</th>
                                        <th>CompLab</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($subjects as $subject)
                                        <tr>
                                            <td>{{ $subject->control_code }}</td>
                                            <td>{{ $subject->course_name }}</td>
                                            <td>{{ $subject->lec }}</td>
                                            <td>{{ $subject->lab }}</td>
                                            <td>{{ $subject->units }}</td>
                                            <td>{{ $subject->level }}</td>
                                            <td>{{ $subject->period }}</td>
                                            <td>{{ $subject->is_complab ? 'Yes' : 'No' }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            No subjects found for this curriculum.
                        </div>
                    @endif
                </div>
                <div class="box-footer">
                    <a href="{{ route('collegeadmin.course_offering_curriculum.program', $program->program_code) }}" class="btn btn-default">Back to Program</a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
