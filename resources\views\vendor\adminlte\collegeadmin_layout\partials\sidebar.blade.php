<!-- Left side column. contains the logo and sidebar -->
<aside class="main-sidebar">

    <!-- sidebar: style can be found in sidebar.less -->
    <section class="sidebar">

        <!-- Sidebar user panel (optional) -->
        <div class="user-panel">
            <div class="pull-left image">
                <img src="{{ asset('/img/user2-160x160.jpg') }}" class="img-circle" alt="User Image" />
            </div>
            <div class="pull-left info">
                <p>{{ Auth::user()->name }} {{ Auth::user()->lastname }}</p>
                <!-- Status -->
                <a href="#"><i class="fa fa-circle text-success"></i> {{ trans('adminlte_lang::message.online') }}</a>
            </div>
        </div>

        <!-- search form (Optional) -->
        <form action="#" method="get" class="sidebar-form">
            <div class="input-group">
                <input type="text" name="q" class="form-control" placeholder="{{ trans('adminlte_lang::message.search') }}..."/>
              <span class="input-group-btn">
                <button type='submit' name='search' id='search-btn' class="btn btn-flat"><i class="fa fa-search"></i></button>
              </span>
            </div>
        </form>
        <!-- /.search form -->

        <!-- Sidebar Menu -->
        <ul class="sidebar-menu">
            <li class="header">MAIN NAVIGATION</li>
            <!-- Optionally, you can add icons to the links -->
            <li class="active"><a href="{{ url('/collegeadmin/dashboard') }}"><i class='fa fa-dashboard'></i> <span>Dashboard</span></a></li>
            <br>

            <!-- FACULTY MANAGEMENT -->
            <li class="treeview">
                <a href="#"><i class="fa fa-users"></i> <span>Faculty Management</span>
                    <span class="pull-right-container">
                        <i class="fa fa-angle-left pull-right"></i>
                    </span>
                </a>
                <ul class="treeview-menu">
                    <li><a href="{{ url('/collegeadmin/faculty/view') }}"><i class='fa fa-list'></i> <span>View Faculty</span></a></li>
                    <li><a href="{{ url('/collegeadmin/faculty/add') }}"><i class='fa fa-plus'></i> <span>Add Faculty</span></a></li>
                    <li><a href="{{ url('/collegeadmin/faculty_loading') }}"><i class='fa fa-tasks'></i> <span>Faculty Loading</span></a></li>
                    <li><a href="{{ url('/collegeadmin/faculty_loading/faculty_loading') }}"><i class='fa fa-calendar'></i> <span>Faculty Loading (New)</span></a></li>
                </ul>
            </li>
            <br>

            <!-- CURRICULUM MANAGEMENT -->
            <li class="treeview">
                <a href="#"><i class="fa fa-folder"></i> <span>Curriculum Management</span>
                    <span class="pull-right-container">
                        <i class="fa fa-angle-left pull-right"></i>
                    </span>
                </a>
                <ul class="treeview-menu">
                    <li><a href="{{ url('/collegeadmin/curriculum') }}"><i class='fa fa-edit'></i> <span>Curriculum</span></a></li>
                    <li><a href="{{ url('/collegeadmin/curriculum/archived_subjects') }}"><i class='fa fa-archive'></i> <span>Archives for Subjects</span></a></li>
                    <li><a href="{{ url('/collegeadmin/course_offerings') }}"><i class='fa fa-edit'></i> <span>Course Offering</span></a></li>
                    <li><a href="{{ url('/collegeadmin/course_offerings/curriculum') }}"><i class='fa fa-book'></i> <span>Course Offering Curriculum</span></a></li>
                </ul>
            </li>
            <br>

            <!-- ROOM MANAGEMENT -->
            <li><a href="{{ url('/collegeadmin/room_management') }}"><i class='fa fa-building'></i> <span>Room Management</span></a></li>
            <br>

            <!-- REPORTS -->
            <li class="treeview">
                <a href="#"><i class="fa fa-pencil"></i> <span>Reports</span>
                    <span class="pull-right-container">
                        <i class="fa fa-angle-left pull-right"></i>
                    </span>
                </a>
                <ul class="treeview-menu">
                    <li><a href="{{ url('/collegeadmin/reports/faculty') }}"><i class="fa fa-circle-o"></i> <span>Faculty Reports</span></a></li>
                    <li><a href="{{ url('/collegeadmin/reports/rooms') }}"><i class="fa fa-circle-o"></i> <span>Rooms Occupied</span></a></li>
                </ul>
            </li>
            <br>

            <!-- PASSWORD -->
            <li class=""><a href="{{ url('/account/change_password') }}"><i class='fa fa-lock'></i> <span>Change Password</span></a></li>
        </ul><!-- /.sidebar-menu -->
    </section>
    <!-- /.sidebar -->
</aside>
