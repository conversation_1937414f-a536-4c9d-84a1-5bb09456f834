<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('title', 'CLASSMOSS - Archived Curriculum Subjects')

@section('content')
<section class="content-header">
    <h1>
        <i class="fa fa-archive"></i> Archived Curriculum Subjects
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{ url('/collegeadmin/dashboard') }}"><i class="fa fa-dashboard"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.curriculum.index') }}">Curriculum Management</a></li>
        <li class="active">Archived Subjects</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-sm-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Archived Curriculum Subjects</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ route('collegeadmin.curriculum.index') }}" class="btn btn-flat btn-default"><i class="fa fa-arrow-left"></i> Back to Curriculum</a>
                    </div>
                </div>
                <div class="box-body">
                    @if(Session::has('success'))
                        <div class="alert alert-success">
                            {{ Session::get('success') }}
                        </div>
                    @endif

                    @if(Session::has('error'))
                        <div class="alert alert-danger">
                            {{ Session::get('error') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Program Code</th>
                                    <th>Curriculum Year</th>
                                    <th>Course Code</th>
                                    <th>Course Name</th>
                                    <th>Level</th>
                                    <th>Period</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($archivedSubjects as $subject)
                                    <tr>
                                        <td>{{ $subject->program_code }}</td>
                                        <td>{{ $subject->curriculum_year }}</td>
                                        <td>{{ $subject->control_code }}</td>
                                        <td>{{ $subject->course_name }}</td>
                                        <td>{{ $subject->level }}</td>
                                        <td>{{ $subject->period }}</td>
                                        <td>
                                            <a href="{{ route('collegeadmin.curriculum.restore_subject', $subject->id) }}" class="btn btn-flat btn-success btn-sm" title="Restore Subject" onclick="return confirm('Are you sure you want to restore this subject?')">
                                                <i class="fa fa-recycle"></i> Restore
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
