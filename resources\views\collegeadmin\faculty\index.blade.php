<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-users"></i>
        Faculty Management
        <small>{{ isset($college) ? $college->college_name : Auth::user()->college_code }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li class="active">Faculty Management</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Faculty Members</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ route('collegeadmin.faculty.create') }}" class="btn btn-primary btn-sm">
                            <i class="fa fa-plus"></i> Add New Faculty
                        </a>
                    </div>
                </div>
                <div class="box-body">
                    @if(Session::has('success'))
                        <div class="alert alert-success">
                            {{ Session::get('success') }}
                        </div>
                    @endif

                    @if(Session::has('error'))
                        <div class="alert alert-danger">
                            {{ Session::get('error') }}
                        </div>
                    @endif

                    @if(count($faculty) > 0)
                        <div class="table-responsive">
                            <table id="faculty-table" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Faculty Status</th>
                                        <th>Department</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($faculty as $member)
                                        @php
                                            $info = $member->instructorInfo;
                                        @endphp
                                        <tr>
                                            <td>{{ $member->username ?? $member->id }}</td>
                                            <td>{{ strtoupper($member->lastname) }}, {{ strtoupper($member->name) }} {{ strtoupper($member->middlename) }}</td>
                                            <td>{{ $member->email }}</td>
                                            <td>{{ $info ? $info->employee_type : 'N/A' }}</td>
                                            <td>{{ $info ? $info->department : 'N/A' }}</td>
                                            <td>
                                                <a href="{{ route('collegeadmin.faculty.show', $member->id) }}" class="btn btn-info btn-sm">
                                                    <i class="fa fa-eye"></i> View
                                                </a>
                                                <a href="{{ route('collegeadmin.faculty.edit', $member->id) }}" class="btn btn-primary btn-sm">
                                                    <i class="fa fa-pencil"></i> Edit
                                                </a>
                                                <a href="{{ route('collegeadmin.faculty_loading.generate_schedule', $member->id) }}" class="btn btn-success btn-sm">
                                                    <i class="fa fa-calendar"></i> Schedule
                                                </a>
                                                <form action="{{ route('collegeadmin.faculty.destroy', $member->id) }}" method="POST" style="display: inline-block;">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to deactivate this faculty member?')">
                                                        <i class="fa fa-trash"></i> Deactivate
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            No faculty members found for your college.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script src="{{ asset('plugins/datatables/jquery.dataTables.js') }}"></script>
<script src="{{ asset('plugins/datatables/dataTables.bootstrap.js') }}"></script>
<script>
    $(function() {
        $('#faculty-table').DataTable({
            "paging": true,
            "lengthChange": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false
        });
    });
</script>
@endpush
