<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-dashboard"></i>
        College Admin Dashboard
        <small>{{ $college->college_name ?? 'College' }} ({{ $college->college_code ?? 'N/A' }})</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li class="active">Dashboard</li>
    </ol>
</section>

<section class="content">
    <!-- Info boxes -->
    <div class="row">
        <div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-aqua"><i class="fa fa-users"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Faculty</span>
                    <span class="info-box-number">{{ $facultyCount ?? 0 }}</span>
                </div>
                <!-- /.info-box-content -->
            </div>
            <!-- /.info-box -->
        </div>
        <!-- /.col -->
        <div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-green"><i class="fa fa-building"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Rooms</span>
                    <span class="info-box-number">{{ $roomsCount ?? 0 }}</span>
                </div>
                <!-- /.info-box-content -->
            </div>
            <!-- /.info-box -->
        </div>
        <!-- /.col -->
        <div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-yellow"><i class="fa fa-graduation-cap"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Programs</span>
                    <span class="info-box-number">{{ $curriculumCount ?? 0 }}</span>
                </div>
                <!-- /.info-box-content -->
            </div>
            <!-- /.info-box -->
        </div>
        <!-- /.col -->
        <div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-red"><i class="fa fa-calendar"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Active Schedules</span>
                    <span class="info-box-number">{{ $schedulesCount ?? 0 }}</span>
                </div>
                <!-- /.info-box-content -->
            </div>
            <!-- /.info-box -->
        </div>
        <!-- /.col -->
    </div>
    <!-- /.row -->

    <div class="row">
        <div class="col-md-8">
            <!-- Calendar View -->
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Class Schedule Overview</h3>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                    </div>
                </div>
                <div class="box-body">
                    <div id="calendar"></div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Room Availability -->
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title">Room Availability</h3>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                    </div>
                </div>
                <div class="box-body">
                    <div id="room-availability">
                        <p>Loading room availability data...</p>
                    </div>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="box box-success">
                <div class="box-header with-border">
                    <h3 class="box-title">Quick Links</h3>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                    </div>
                </div>
                <div class="box-body">
                    <ul class="nav nav-stacked">
                        <li><a href="{{ url('/collegeadmin/faculty/view') }}">Manage Faculty <i class="fa fa-arrow-circle-right pull-right"></i></a></li>
                        <li><a href="{{ url('/collegeadmin/curriculum') }}">Manage Curriculum <i class="fa fa-arrow-circle-right pull-right"></i></a></li>
                        <li><a href="{{ url('/collegeadmin/room_management') }}">Manage Rooms <i class="fa fa-arrow-circle-right pull-right"></i></a></li>
                        <li><a href="{{ url('/collegeadmin/faculty_loading') }}">Faculty Loading <i class="fa fa-arrow-circle-right pull-right"></i></a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('footer-script')
<link rel="stylesheet" href="{{ asset('plugins/fullcalendar/fullcalendar.min.css') }}">
<script src="{{ asset('plugins/fullcalendar/moment.min.js') }}"></script>
<script src="{{ asset('plugins/fullcalendar/fullcalendar.min.js') }}"></script>
<script>
$(function() {
    // Initialize calendar
    $('#calendar').fullCalendar({
        header: {
            left: 'prev,next today',
            center: 'title',
            right: 'month,agendaWeek,agendaDay'
        },
        defaultView: 'agendaWeek',
        minTime: '07:00:00',
        maxTime: '22:00:00',
        hiddenDays: [0], // Hide Sunday
        firstDay: 1, // Monday as first day
        height: 500,
        allDaySlot: false,
        columnFormat: 'ddd',
        eventSources: [
            {
                url: "{{ url('/ajax/collegeadmin/dashboard/schedule_data') }}",
                type: 'GET',
                error: function() {
                    alert('There was an error while fetching events!');
                }
            }
        ],
        eventRender: function(event, element) {
            element.find('div.fc-title').html(element.find('div.fc-title').text());
        }
    });

    // Load room availability data
    $.ajax({
        url: "{{ url('/ajax/collegeadmin/dashboard/room_availability') }}",
        type: 'GET',
        success: function(data) {
            $('#room-availability').html(data);
        },
        error: function() {
            $('#room-availability').html('<p class="text-danger">Error loading room availability data.</p>');
        }
    });
});
</script>
@endsection
