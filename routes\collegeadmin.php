<?php

// College Admin Dashboard
Route::get('/collegeadmin/dashboard', 'CollegeAdmin\DashboardController@index');
Route::get('/ajax/collegeadmin/dashboard/schedule_data', 'CollegeAdmin\Ajax\DashboardAjaxController@getScheduleData');
Route::get('/ajax/collegeadmin/dashboard/room_availability', 'CollegeAdmin\Ajax\DashboardAjaxController@getRoomAvailability');

// Faculty Management
Route::get('/collegeadmin/faculty/view', 'CollegeAdmin\FacultyController@index')->name('collegeadmin.faculty.index');
Route::get('/collegeadmin/faculty/add', 'CollegeAdmin\FacultyController@create')->name('collegeadmin.faculty.create');
Route::post('/collegeadmin/faculty/store', 'CollegeAdmin\FacultyController@store')->name('collegeadmin.faculty.store');
Route::get('/collegeadmin/faculty/show/{id}', 'CollegeAdmin\FacultyController@show')->name('collegeadmin.faculty.show');
Route::get('/collegeadmin/faculty/edit/{id}', 'CollegeAdmin\FacultyController@edit')->name('collegeadmin.faculty.edit');
Route::put('/collegeadmin/faculty/update/{id}', 'CollegeAdmin\FacultyController@update')->name('collegeadmin.faculty.update');
Route::delete('/collegeadmin/faculty/destroy/{id}', 'CollegeAdmin\FacultyController@destroy')->name('collegeadmin.faculty.destroy');

// Faculty Loading
Route::get('/collegeadmin/faculty_loading', 'CollegeAdmin\FacultyLoadingController@index')->name('collegeadmin.faculty_loading.index');
Route::get('/collegeadmin/faculty_loading/faculty_loading', 'CollegeAdmin\FacultyLoadingController@faculty_loading');
Route::get('/collegeadmin/faculty_loading/generate_schedule/{instructor}', 'CollegeAdmin\FacultyLoadingController@generateSchedule')->name('collegeadmin.faculty_loading.generate_schedule');
Route::get('/ajax/collegeadmin/faculty_loading/courses_to_load', 'CollegeAdmin\Ajax\FacultyLoadingAjax@coursesToLoad');
Route::get('/ajax/collegeadmin/faculty_loading/current_load', 'CollegeAdmin\Ajax\FacultyLoadingAjax@currentLoad');
Route::get('/ajax/collegeadmin/faculty_loading/add_faculty_load', 'CollegeAdmin\Ajax\FacultyLoadingAjax@addFacultyLoad');
Route::get('/ajax/collegeadmin/faculty_loading/remove_faculty_load', 'CollegeAdmin\Ajax\FacultyLoadingAjax@removeFacultyLoad');
Route::get('/ajax/collegeadmin/faculty_loading/search_courses', 'CollegeAdmin\Ajax\FacultyLoadingAjax@searchCourses');
Route::get('/ajax/collegeadmin/faculty_loading/get_units_loaded', 'CollegeAdmin\Ajax\FacultyLoadingAjax@get_units_loaded');
Route::get('/ajax/collegeadmin/faculty_loading/override_add', 'CollegeAdmin\Ajax\FacultyLoadingAjax@override_add');

// Curriculum Management
Route::get('/collegeadmin/curriculum', 'CollegeAdmin\CurriculumController@index')->name('collegeadmin.curriculum.index');
Route::get('/collegeadmin/curriculum/create', 'CollegeAdmin\CurriculumController@create')->name('collegeadmin.curriculum.create');
Route::post('/collegeadmin/curriculum/store', 'CollegeAdmin\CurriculumController@store')->name('collegeadmin.curriculum.store');
Route::get('/collegeadmin/curriculum/view_curricula/{program_code}', 'CollegeAdmin\CurriculumController@viewCurricula')->name('collegeadmin.curriculum.view_curricula');
Route::get('/collegeadmin/curriculum/list_curriculum/{program_code}/{curriculum_year}', 'CollegeAdmin\CurriculumController@listCurriculum')->name('collegeadmin.curriculum.list_curriculum');
Route::get('/collegeadmin/curriculum/archive_subject/{id}', 'CollegeAdmin\CurriculumController@archiveSubject')->name('collegeadmin.curriculum.archive_subject');
Route::get('/collegeadmin/curriculum/archived_subjects', 'CollegeAdmin\CurriculumController@archivedSubjects')->name('collegeadmin.curriculum.archived_subjects');
Route::get('/collegeadmin/curriculum/restore_subject/{id}', 'CollegeAdmin\CurriculumController@restoreSubject')->name('collegeadmin.curriculum.restore_subject');
Route::get('/collegeadmin/curriculum/fix_data', 'CollegeAdmin\CurriculumController@fixCurriculumData')->name('collegeadmin.curriculum.fix_data');
Route::post('/collegeadmin/curriculum/edit_curriculum', 'CollegeAdmin\CurriculumController@editCurriculum')->name('collegeadmin.curriculum.edit_curriculum');
Route::get('/ajax/collegeadmin/curriculum/edit_modal', 'CollegeAdmin\Ajax\CurriculumAjax@editModal');

// Room Management
Route::get('/collegeadmin/room_management', 'CollegeAdmin\RoomController@index')->name('collegeadmin.room.index');
Route::post('/collegeadmin/room_management/store', 'CollegeAdmin\RoomController@store')->name('collegeadmin.room.store');
Route::get('/collegeadmin/room_management/edit/{id}', 'CollegeAdmin\RoomController@edit')->name('collegeadmin.room.edit');
Route::put('/collegeadmin/room_management/update/{id}', 'CollegeAdmin\RoomController@update')->name('collegeadmin.room.update');
Route::get('/collegeadmin/room_management/archive/{id}', 'CollegeAdmin\RoomController@archive')->name('collegeadmin.room.archive');
Route::get('/collegeadmin/room_management/archived', 'CollegeAdmin\RoomController@archived')->name('collegeadmin.room.archived');
Route::get('/collegeadmin/room_management/restore/{id}', 'CollegeAdmin\RoomController@restore')->name('collegeadmin.room.restore');
Route::get('/ajax/collegeadmin/room_management/edit_room', 'CollegeAdmin\Ajax\RoomAjax@editRoom');
Route::get('/ajax/collegeadmin/room_management/search', 'CollegeAdmin\Ajax\RoomAjax@searchRooms');
Route::get('/ajax/collegeadmin/room_management/search_archive', 'CollegeAdmin\Ajax\RoomAjax@searchRoomsArchive');

// Reports
Route::get('/collegeadmin/reports/faculty', 'CollegeAdmin\ReportsController@facultyReports')->name('collegeadmin.reports.faculty');
Route::get('/collegeadmin/reports/rooms', 'CollegeAdmin\ReportsController@roomsReports')->name('collegeadmin.reports.rooms');

// Course Offerings
Route::get('/collegeadmin/course_offerings', 'CollegeAdmin\CourseOfferingController@index')->name('collegeadmin.course_offering.index');
Route::get('/collegeadmin/course_offerings/program/{program_code}', 'CollegeAdmin\CourseOfferingController@viewProgramOfferings')->name('collegeadmin.course_offering.program');
Route::get('/collegeadmin/course_offerings/section/{section_id}', 'CollegeAdmin\CourseOfferingController@viewSectionOfferings')->name('collegeadmin.course_offering.section');
Route::get('/collegeadmin/course_offerings/section/add_all/{section_id}', 'CollegeAdmin\CourseOfferingController@addAllCurricula')->name('collegeadmin.course_offering.add_all');
Route::post('/collegeadmin/course_offerings/section/add_selected/{section_id}', 'CollegeAdmin\CourseOfferingController@addSelectedCurricula')->name('collegeadmin.course_offering.add_selected');
Route::get('/collegeadmin/course_offerings/section/remove/{section_id}/{offering_id}', 'CollegeAdmin\CourseOfferingController@removeCurriculum')->name('collegeadmin.course_offering.remove');
Route::patch('/collegeadmin/course_offerings/section/update_semester/{section_id}/{offering_id}', 'CollegeAdmin\CourseOfferingController@updateSemester')->name('collegeadmin.course_offering.update_semester');

// Test route for debugging
Route::get('/collegeadmin/test_data', function () {
    $user = Auth::user();
    $collegeCode = $user->college_code;
    $college = App\College::where('college_code', $collegeCode)->first();

    $programService = new App\Services\ProgramService();
    $programs = $programService->getProgramsByCollege($collegeCode);

    return response()->json([
        'user' => $user,
        'college' => $college,
        'programs' => $programs
    ]);
});

// Course Offerings AJAX
Route::get('/ajax/collegeadmin/course_offerings/get_programs', 'CollegeAdmin\Ajax\CourseOfferingAjax@getPrograms')->name('ajax.collegeadmin.course_offering.get_programs');
Route::get('/ajax/collegeadmin/course_offerings/get_curriculum_years', 'CollegeAdmin\Ajax\CourseOfferingAjax@getCurriculumYears')->name('ajax.collegeadmin.course_offering.get_curriculum_years');
Route::get('/ajax/collegeadmin/course_offerings/get_sections', 'CollegeAdmin\Ajax\CourseOfferingAjax@getSections')->name('ajax.collegeadmin.course_offering.get_sections');
Route::get('/ajax/collegeadmin/course_offerings/get_available_curricula', 'CollegeAdmin\Ajax\CourseOfferingAjax@getAvailableCurricula')->name('ajax.collegeadmin.course_offering.get_available_curricula');
Route::get('/ajax/collegeadmin/course_offerings/get_offered_curricula', 'CollegeAdmin\Ajax\CourseOfferingAjax@getOfferedCurricula')->name('ajax.collegeadmin.course_offering.get_offered_curricula');

// Course Offering Curriculum
Route::get('/collegeadmin/course_offerings/curriculum', 'CollegeAdmin\CourseOfferingCurriculumController@index')->name('collegeadmin.course_offering_curriculum.index');
Route::get('/collegeadmin/course_offerings/curriculum/program/{program_code}', 'CollegeAdmin\CourseOfferingCurriculumController@viewProgramOfferings')->name('collegeadmin.course_offering_curriculum.program');
Route::get('/collegeadmin/course_offerings/curriculum/details/{program_code}/{curriculum_year}', 'CollegeAdmin\CourseOfferingCurriculumController@viewCurriculumDetails')->name('collegeadmin.course_offering_curriculum.details');
Route::get('/collegeadmin/course_offerings/curriculum/section/{section_id}', 'CollegeAdmin\CourseOfferingCurriculumController@viewSectionOfferings')->name('collegeadmin.course_offering_curriculum.section');
Route::get('/collegeadmin/course_offerings/curriculum/section/add_all/{section_id}', 'CollegeAdmin\CourseOfferingCurriculumController@addAllCurricula')->name('collegeadmin.course_offering_curriculum.add_all');
Route::post('/collegeadmin/course_offerings/curriculum/section/add_selected/{section_id}', 'CollegeAdmin\CourseOfferingCurriculumController@addSelectedCurricula')->name('collegeadmin.course_offering_curriculum.add_selected');
Route::get('/collegeadmin/course_offerings/curriculum/section/remove/{section_id}/{offering_id}', 'CollegeAdmin\CourseOfferingCurriculumController@removeCurriculum')->name('collegeadmin.course_offering_curriculum.remove');
Route::patch('/collegeadmin/course_offerings/curriculum/section/update_semester/{section_id}/{offering_id}', 'CollegeAdmin\CourseOfferingCurriculumController@updateSemester')->name('collegeadmin.course_offering_curriculum.update_semester');

// Course Offering Curriculum AJAX
Route::get('/ajax/collegeadmin/course_offerings/curriculum/get_programs', 'CollegeAdmin\Ajax\CourseOfferingCurriculumAjax@getPrograms')->name('ajax.collegeadmin.course_offering_curriculum.get_programs');
Route::get('/ajax/collegeadmin/course_offerings/curriculum/get_curriculum_years', 'CollegeAdmin\Ajax\CourseOfferingCurriculumAjax@getCurriculumYears')->name('ajax.collegeadmin.course_offering_curriculum.get_curriculum_years');
Route::get('/ajax/collegeadmin/course_offerings/curriculum/get_sections', 'CollegeAdmin\Ajax\CourseOfferingCurriculumAjax@getSections')->name('ajax.collegeadmin.course_offering_curriculum.get_sections');
Route::get('/ajax/collegeadmin/course_offerings/curriculum/get_curriculum_details', 'CollegeAdmin\Ajax\CourseOfferingCurriculumAjax@getCurriculumDetails')->name('ajax.collegeadmin.course_offering_curriculum.get_curriculum_details');
Route::get('/ajax/collegeadmin/course_offerings/curriculum/get_available_curricula', 'CollegeAdmin\Ajax\CourseOfferingCurriculumAjax@getAvailableCurricula')->name('ajax.collegeadmin.course_offering_curriculum.get_available_curricula');
Route::get('/ajax/collegeadmin/course_offerings/curriculum/get_section_offerings', 'CollegeAdmin\Ajax\CourseOfferingCurriculumAjax@getSectionOfferings')->name('ajax.collegeadmin.course_offering_curriculum.get_section_offerings');
