<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-user"></i>
        Faculty Details
        <small>{{ Auth::user()->college_code }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.faculty.index') }}">Faculty Management</a></li>
        <li class="active">Faculty Details</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Faculty Information</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ route('collegeadmin.faculty.edit', $faculty->id) }}" class="btn btn-primary btn-sm">
                            <i class="fa fa-pencil"></i> Edit
                        </a>
                    </div>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 30%">ID</th>
                                    <td>{{ $faculty->id }}</td>
                                </tr>
                                <tr>
                                    <th>Username</th>
                                    <td>{{ $faculty->username }}</td>
                                </tr>
                                <tr>
                                    <th>First Name</th>
                                    <td>{{ $faculty->name }}</td>
                                </tr>
                                <tr>
                                    <th>Middle Name</th>
                                    <td>{{ $faculty->middlename }}</td>
                                </tr>
                                <tr>
                                    <th>Last Name</th>
                                    <td>{{ $faculty->lastname }}</td>
                                </tr>
                                <tr>
                                    <th>Extension Name</th>
                                    <td>{{ $faculty->extensionname }}</td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td>{{ $faculty->email }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="box box-info">
                                <div class="box-header with-border">
                                    <h3 class="box-title">Current Teaching Load</h3>
                                </div>
                                <div class="box-body">
                                    <div id="teaching-load">
                                        <p>Loading teaching load data...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="box-footer">
                    <a href="{{ route('collegeadmin.faculty.index') }}" class="btn btn-default">Back to List</a>
                    <a href="{{ route('collegeadmin.faculty_loading.generate_schedule', $faculty->id) }}" class="btn btn-success pull-right">
                        <i class="fa fa-calendar"></i> View Schedule
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('footer-script')
<script>
$(function() {
    // Load teaching load data
    $.ajax({
        url: "{{ url('/ajax/collegeadmin/faculty_loading/current_load') }}",
        type: 'GET',
        data: { instructor_id: {{ $faculty->id }} },
        success: function(data) {
            $('#teaching-load').html(data);
        },
        error: function() {
            $('#teaching-load').html('<p class="text-danger">Error loading teaching load data.</p>');
        }
    });
});
</script>
@endsection
