<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('title', 'CLASSMOSS - Course Offering Curriculum for ' . $program->program_code)

@section('main-content')
<section class="content-header">
    <h1>
        Course Offering Curriculum for {{ $program->program_code }}
        <small>{{ $program->program_name }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{ url('/collegeadmin/dashboard') }}"><i class="fa fa-dashboard"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.course_offering_curriculum.index') }}">Course Offering Curriculum</a></li>
        <li class="active">{{ $program->program_code }}</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Curriculum Years</h3>
                </div>
                <div class="box-body">
                    @if(Session::has('success'))
                        <div class="alert alert-success">
                            {{ Session::get('success') }}
                        </div>
                    @endif

                    @if(Session::has('error'))
                        <div class="alert alert-danger">
                            {{ Session::get('error') }}
                        </div>
                    @endif

                    @if(count($curriculumYears) > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Curriculum Year</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($curriculumYears as $year)
                                        <tr>
                                            <td>{{ $year }}</td>
                                            <td>
                                                <a href="{{ route('collegeadmin.course_offering_curriculum.details', [$program->program_code, $year]) }}" class="btn btn-primary btn-sm">
                                                    <i class="fa fa-list"></i> View Curriculum
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            No curriculum years found for this program.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Sections</h3>
                </div>
                <div class="box-body">
                    @if(count($sections) > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Section Name</th>
                                        <th>Level</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sections as $section)
                                        <tr>
                                            <td>{{ $section->section_name }}</td>
                                            <td>{{ $section->level }}</td>
                                            <td>
                                                <a href="{{ route('collegeadmin.course_offering_curriculum.section', $section->id) }}" class="btn btn-primary btn-sm">
                                                    <i class="fa fa-list"></i> View Offerings
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            No sections found for this program.
                        </div>
                    @endif
                </div>
                <div class="box-footer">
                    <a href="{{ route('collegeadmin.course_offering_curriculum.index') }}" class="btn btn-default">Back to Programs</a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
